#!/usr/bin/env python3
"""
测试OpenAI SDK调用自定义API
"""

from openai import OpenAI

# 配置
CONFIG = {
    "api_key": "sk-y3dYMll9oWtQNgj2I9dKO8UAHS24tcMgr6alaDl0EWfqU5Db",
    "base_url": "http://ai.ai.iot.chinamobile.com/imaas/v1/",
    "model": "DeepSeek-R1"
}

def test_openai_sdk():
    """测试OpenAI SDK调用"""
    print("🧪 测试OpenAI SDK调用自定义API")
    print("=" * 50)
    
    try:
        # 创建OpenAI客户端
        client = OpenAI(
            api_key=CONFIG["api_key"],
            base_url=CONFIG["base_url"]
        )
        
        print(f"📡 API地址: {CONFIG['base_url']}")
        print(f"🤖 模型: {CONFIG['model']}")
        print(f"🔑 API Key: {CONFIG['api_key'][:10]}...")
        
        # 测试非流式调用
        print("\n🔄 测试非流式调用...")
        print("用户: 你好，请简单介绍一下你自己")
        
        completion = client.chat.completions.create(
            model=CONFIG["model"],
            messages=[
                {'role': 'system', 'content': 'You are a helpful assistant.'},
                {'role': 'user', 'content': '你好，请简单介绍一下你自己'}
            ]
        )
        
        response = completion.choices[0].message.content
        print(f"✅ 非流式调用成功!")
        print(f"回复: {response}")
        
        # 测试流式调用
        print("\n🌊 测试流式调用...")
        print("用户: 请用一句话介绍Elasticsearch")
        print("回复: ", end="", flush=True)
        
        completion = client.chat.completions.create(
            model=CONFIG["model"],
            messages=[
                {'role': 'system', 'content': 'You are a helpful assistant.'},
                {'role': 'user', 'content': '请用一句话介绍Elasticsearch'}
            ],
            stream=True
        )
        
        full_response = ""
        for chunk in completion:
            if chunk.choices[0].delta.content:
                content = chunk.choices[0].delta.content
                print(content, end="", flush=True)
                full_response += content
        
        print(f"\n✅ 流式调用成功!")
        print(f"完整回复长度: {len(full_response)} 字符")
        
        print("\n🎉 所有测试通过!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        
        # 详细错误信息
        if hasattr(e, 'response'):
            print(f"HTTP状态码: {e.response.status_code}")
            print(f"响应内容: {e.response.text}")

if __name__ == "__main__":
    test_openai_sdk()

#!/usr/bin/env python3
"""
测试HTTP方式调用LLM API
"""

import asyncio
import json
import httpx

# 配置
CONFIG = {
    "base_url": "https://api.deepseek.com/v1",
    "api_key": "***********************************",
    "model": "deepseek-reasoner"
}

async def test_http_llm_api():
    """测试HTTP方式调用LLM API"""
    print("🧪 测试HTTP方式调用LLM API")
    print("=" * 50)
    
    try:
        # 创建HTTP客户端
        async with httpx.AsyncClient(timeout=30.0) as client:
            print(f"📡 API地址: {CONFIG['base_url']}")
            print(f"🤖 模型: {CONFIG['model']}")
            print(f"🔑 API Key: {CONFIG['api_key'][:10]}...")
            
            # 构建请求数据
            request_data = {
                "model": CONFIG["model"],
                "stream": True,
                "messages": [
                    {"role": "system", "content": "You are a helpful assistant."},
                    {"role": "user", "content": "你好，请简单介绍一下你自己"}
                ]
            }
            
            # 构建请求头
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {CONFIG['api_key']}"
            }
            
            print("\n💬 发送测试消息...")
            print("用户: 你好，请简单介绍一下你自己")
            print("回复: ", end="", flush=True)
            
            # 发送流式请求
            url = f"{CONFIG['base_url']}/chat/completions"
            
            async with client.stream(
                "POST", 
                url, 
                json=request_data, 
                headers=headers
            ) as response:
                print(f"\n📊 HTTP状态码: {response.status_code}")
                
                if response.status_code != 200:
                    error_content = await response.aread()
                    print(f"❌ 请求失败: {error_content.decode()}")
                    return
                
                print("🌊 开始接收流式响应:")
                print("回复: ", end="", flush=True)
                
                full_response = ""
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data_str = line[6:]  # 移除 "data: " 前缀
                        
                        if data_str.strip() == "[DONE]":
                            print("\n✅ 流式响应完成!")
                            break
                        
                        try:
                            data = json.loads(data_str)
                            if "choices" in data and len(data["choices"]) > 0:
                                delta = data["choices"][0].get("delta", {})
                                if "content" in delta and delta["content"]:
                                    content = delta["content"]
                                    print(content, end="", flush=True)
                                    full_response += content
                        except json.JSONDecodeError as e:
                            print(f"\n⚠️ JSON解析错误: {e}")
                            print(f"原始数据: {data_str}")
                            continue
                
                print(f"\n\n📏 完整回复长度: {len(full_response)} 字符")
                print("🎉 HTTP调用测试成功!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print(f"错误类型: {type(e).__name__}")

async def test_non_stream():
    """测试非流式调用"""
    print("\n🔄 测试非流式调用")
    print("=" * 30)
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            # 构建请求数据（非流式）
            request_data = {
                "model": CONFIG["model"],
                "stream": False,
                "messages": [
                    {"role": "system", "content": "You are a helpful assistant."},
                    {"role": "user", "content": "请用一句话介绍你自己"}
                ]
            }
            
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {CONFIG['api_key']}"
            }
            
            url = f"{CONFIG['base_url']}/chat/completions"
            
            response = await client.post(url, json=request_data, headers=headers)
            
            print(f"📊 HTTP状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if "choices" in data and len(data["choices"]) > 0:
                    content = data["choices"][0]["message"]["content"]
                    print(f"✅ 非流式调用成功!")
                    print(f"回复: {content}")
                else:
                    print("❌ 响应格式异常")
                    print(f"响应内容: {data}")
            else:
                print(f"❌ 请求失败: {response.text}")
        
    except Exception as e:
        print(f"❌ 非流式测试失败: {e}")

def show_curl_command():
    """显示等效的curl命令"""
    print("\n🔧 等效的curl命令:")
    print("=" * 50)
    
    curl_command = f"""curl -i -X POST \\
   -H "Content-Type:application/json" \\
   -H "Authorization:Bearer {CONFIG['api_key']}" \\
   -d '{{
     "model": "{CONFIG['model']}", 
     "stream": true, 
     "messages": [
       {{"role": "system", "content": "You are a helpful assistant."}}, 
       {{"role": "user", "content": "你好，请简单介绍一下你自己"}}
     ]
   }}' \\
   '{CONFIG['base_url']}/chat/completions'"""
   
    print(curl_command)

if __name__ == "__main__":
    asyncio.run(test_http_llm_api())
    asyncio.run(test_non_stream())
    show_curl_command()
